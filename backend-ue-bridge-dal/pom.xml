<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.deepinnet</groupId>
        <artifactId>backend-ue-bridge</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <groupId>com.deepinnet</groupId>
    <artifactId>backend-ue-bridge-dal</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>backend-ue-bridge-dal</name>
    <description>UE桥接服务数据访问层</description>

<!--    <dependencies>-->
<!--        <dependency>-->
<!--            <groupId>com.baomidou</groupId>-->
<!--            <artifactId>mybatis-plus-boot-starter</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.postgresql</groupId>-->
<!--            <artifactId>postgresql</artifactId>-->
<!--            <scope>runtime</scope>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.github.pagehelper</groupId>-->
<!--            <artifactId>pagehelper-spring-boot-starter</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.github.jsqlparser</groupId>-->
<!--            <artifactId>jsqlparser</artifactId>-->
<!--        </dependency>-->
<!--    </dependencies>-->

</project> 