package com.deepinnet.bridge.web.controller;

import com.deepinnet.digitaltwin.common.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 基础Controller
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/bridge")
public class BaseController {

    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("UE Bridge Service is running");
    }
} 