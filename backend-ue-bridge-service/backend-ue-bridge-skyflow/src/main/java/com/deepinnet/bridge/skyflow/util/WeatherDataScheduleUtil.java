package com.deepinnet.bridge.skyflow.util;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.*;
import com.deepinnet.bridge.error.BizErrorCode;
import com.deepinnet.bridge.http.CommonDataService;
import com.deepinnet.bridge.skyflow.dto.*;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.enums.WeatherGridFactor;
import com.deepinnet.spatiotemporalplatform.model.skyflow.*;
import com.github.rholder.retry.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> wong
 * @create 2025/7/25 10:29
 * @Description
 */
@Component
@RequiredArgsConstructor
public class WeatherDataScheduleUtil {

    private final CommonDataService commonDataService;

    @Value("${weather.node.url}")
    private String weatherNodeUrl;

    @Value("${weather.node.wktUrl}")
    private String weatherWktUrl;

    /**
     * 指定区域的WKT
     */
    private static final String TARGET_WKT = "POLYGON((114.205390 22.681031,114.205390 22.725997,114.254133 22.725997,114.254133 22.681031,114.205390 22.681031))";
    private static final String SMALL_WKT = "POLYGON((114.221836 22.694831,114.227599 22.700747,114.233775 22.696058,114.228426 22.688724,114.227096 22.681499,114.226092 22.679972,114.220152 22.678718,114.217019 22.678009,114.218586 22.680681,114.2229 22.685289,114.223107 22.687197,114.223668 22.689924,114.223728 22.691614,114.221925 22.694422,114.221836 22.694831))";

    /**
     * 细分网格大小（平方公里）
     */
    private static final double FINE_GRID_SIZE_KM2 = 0.0049;

    /**
     * 网格索引信息
     */
    public static class GridIndexInfo {
        private String wkt;
        private List<String> paths;

        public GridIndexInfo() {
            this.paths = new ArrayList<>();
        }

        public GridIndexInfo(String wkt) {
            this.wkt = wkt;
            this.paths = new ArrayList<>();
        }

        public String getWkt() {
            return wkt;
        }

        public void setWkt(String wkt) {
            this.wkt = wkt;
        }

        public List<String> getPaths() {
            return paths;
        }

        public void setPaths(List<String> paths) {
            this.paths = paths;
        }

        public void addPath(String path) {
            this.paths.add(path);
        }
    }

    public void doProcessWeatherData(Boolean firstGridOnly, String tempFilePrefix, String weatherLatestFilePath) {
        LogUtil.info("天气数据定时任务开始执行，目标区域：{}", TARGET_WKT);
        LogUtil.info("是否只解析第一个网格数据：{}", firstGridOnly);

        // 生成批次号（当前时间戳）
        long batchNo = System.currentTimeMillis();

        try {
            // 1. 创建临时目录用于生成新数据
            String tempDir;
            if (StrUtil.isNotBlank(tempFilePrefix)) {
                tempDir = weatherLatestFilePath + "_" + tempFilePrefix + "_temp_" + batchNo;
            } else {
                tempDir = weatherLatestFilePath + "_temp_" + batchNo;
            }

            prepareTemporaryDirectory(tempDir);

            // 2. 使用WktGridUtil切分区域
            List<String> gridWktList = WktGridUtil.splitIntoGrids(TARGET_WKT, 1.0);
            LogUtil.info("区域拆分为{}个网格", gridWktList.size());

            if (CollectionUtils.isEmpty(gridWktList)) {
                LogUtil.warn("区域拆分网格为空，跳过处理");
                // 删除临时目录
                deleteTempDirectory(tempDir);
                return;
            }

            // 3. 用于收集网格索引信息
            List<GridIndexInfo> gridIndexList = new ArrayList<>();

            // 4. 处理每个网格（生成到临时目录）
            int totalGeneratedFiles = 0;
            final int totalGrids = gridWktList.size();
            int level = 5;

            for (int i = 0; i < gridWktList.size(); i++) {
                final String gridWkt = gridWktList.get(i);

                try {
                    GridIndexInfo gridInfo = new GridIndexInfo(gridWkt);
                    int gridFileCount;

                    if (firstGridOnly) {
                        gridFileCount = processFirstGridDataOnly(gridWkt, level, i, totalGrids, batchNo, tempDir, gridInfo);
                    } else {
                        gridFileCount = processGridWeatherDataToTemp(gridWkt, level, i, totalGrids, batchNo, tempDir, gridInfo);
                    }

                    totalGeneratedFiles += gridFileCount;

                    // 只有生成了文件的网格才加入索引
                    if (gridFileCount > 0) {
                        gridIndexList.add(gridInfo);
                    }
                } catch (Exception e) {
                    LogUtil.error("处理网格{}异常", i, e);
                    // 继续处理下一个网格
                }
            }

            // 5. 验证生成的文件数量是否符合预期
            if (totalGeneratedFiles == 0) {
                LogUtil.error("未生成任何天气文件，取消替换操作");
                deleteTempDirectory(tempDir);
                throw new BizException(BizErrorCode.WEATHER_SCHEDULE_TASK_ERROR.getCode(),
                        "未生成任何天气文件");
            }

            // 6. 生成索引文件
            generateIndexFile(gridIndexList, tempDir, batchNo, weatherLatestFilePath);

            // 7. 原子替换：生成成功后替换旧数据
            replaceOldDataWithNew(tempDir, weatherLatestFilePath);

            LogUtil.info("本批次共生成{}个文件，共{}个网格，已成功替换到路径：{}", totalGeneratedFiles, gridIndexList.size(), weatherLatestFilePath);
            LogUtil.info("天气数据定时任务执行完成，批次号：{}", batchNo);
        } catch (Exception e) {
            LogUtil.error("天气数据定时任务执行失败，批次号：{}", batchNo, e);
            // 删除可能存在的临时目录
            String tempDir = weatherLatestFilePath + "_temp_" + batchNo;
            deleteTempDirectory(tempDir);
            throw new BizException(BizErrorCode.WEATHER_SCHEDULE_TASK_ERROR.getCode(),
                    BizErrorCode.WEATHER_SCHEDULE_TASK_ERROR.getDesc());
        }
    }

    /**
     * 只处理第一个网格的数据（其他网格数据丢弃）- 使用Google重试框架
     */
    private int processFirstGridDataOnly(String gridWkt, int level, int gridIndex, int totalGrids, long batchNo, String tempDir, GridIndexInfo gridInfo) {
        // 创建重试器
        Retryer<Integer> retryer = RetryerBuilder.<Integer>newBuilder()
                .retryIfException() // 遇到任何异常都重试
                .withWaitStrategy(WaitStrategies.fixedWait(5, TimeUnit.SECONDS)) // 每次重试等待5秒
                .withStopStrategy(StopStrategies.stopAfterAttempt(3)) // 最多重试3次
                .withRetryListener(new RetryListener() {
                    @Override
                    public <V> void onRetry(Attempt<V> attempt) {
                        if (attempt.hasException()) {
                            LogUtil.warn("处理网格{}异常（只保留第一个数据模式），第{}次重试，异常信息：{}",
                                    gridIndex, attempt.getAttemptNumber(), attempt.getExceptionCause().getMessage());
                        }
                    }
                })
                .build();

        try {
            return retryer.call(() -> processFirstGridDataOnlyInternal(gridWkt, level, gridIndex, totalGrids, batchNo, tempDir, gridInfo));
        } catch (Exception e) {
            LogUtil.error("处理网格{}异常（只保留第一个数据模式），已达到最大重试次数，放弃处理", gridIndex, e);
            return 0; // 返回0而不是抛出异常，避免影响其他网格处理
        }
    }

    /**
     * 只处理第一个网格的数据（其他网格数据丢弃）- 内部实现
     */
    private int processFirstGridDataOnlyInternal(String gridWkt, int level, int gridIndex, int totalGrids, long batchNo, String tempDir, GridIndexInfo gridInfo) throws Exception {
        int gridFileCount = 0;

        // 调用高德获取气象数据
        String weatherDataResponse = invokeRemoteGetWeatherData(gridWkt, level);
        if (StringUtils.isBlank(weatherDataResponse)) {
            LogUtil.info("高德返回气象数据响应为空，跳过网格{}", gridIndex);
            return gridFileCount;
        }

        // 下载并解析气象数据
        JSONObject jsonObject = JSONUtil.parseObj(weatherDataResponse);
        String dataUrl = (String) jsonObject.get("url");

        // 下载完整数据但只保留第一个网格数据
        Map<String, LocationWeatherData> fullWeatherDataMap = WeatherDataProcessUtil.downloadAndDeserializeWeatherData(dataUrl);
        if (MapUtil.isEmpty(fullWeatherDataMap)) {
            LogUtil.warn("网格{}气象数据为空", gridIndex);
            return gridFileCount;
        }

        // 只保留第一个网格数据
        Map<String, LocationWeatherData> firstGridDataMap = new HashMap<>();
        if (!fullWeatherDataMap.isEmpty()) {
            String firstKey = fullWeatherDataMap.keySet().iterator().next();
            firstGridDataMap.put(firstKey, fullWeatherDataMap.get(firstKey));

            LogUtil.info("只保留第一个网格数据，总网格数量：{}，保留网格编码：{}",
                    fullWeatherDataMap.size(), firstKey);
        }

        // 获取网格中心点坐标
        Set<String> gridCodes = firstGridDataMap.keySet();
        Map<String, GridCenterCoordinate> gridCenterMap = WeatherDataProcessUtil.getGridCenterFromNode(weatherNodeUrl, gridCodes);

        // 根据factor生成相应类型的文件（生成到临时目录）
        gridFileCount = generateWeatherFilesToTemp(level, firstGridDataMap, gridCenterMap, totalGrids, gridIndex, batchNo, tempDir, gridInfo);

        LogUtil.info("成功处理网格{}（只保留第一个数据），生成{}个文件", gridIndex, gridFileCount);
        return gridFileCount;
    }

    /**
     * 处理单个网格的气象数据（生成到临时目录）- 使用Google重试框架
     */
    private int processGridWeatherDataToTemp(String gridWkt, int level, int gridIndex, int totalGrids, long batchNo, String tempDir, GridIndexInfo gridInfo) {
        // 创建重试器
        Retryer<Integer> retryer = RetryerBuilder.<Integer>newBuilder()
                .retryIfException() // 遇到任何异常都重试
                .withWaitStrategy(WaitStrategies.fixedWait(5, TimeUnit.SECONDS)) // 每次重试等待5秒
                .withStopStrategy(StopStrategies.stopAfterAttempt(5)) // 最多重试5次
                .withRetryListener(new RetryListener() {
                    @Override
                    public <V> void onRetry(Attempt<V> attempt) {
                        if (attempt.hasException()) {
                            LogUtil.warn("处理网格{}异常，第{}次重试，异常信息：{}",
                                    gridIndex, attempt.getAttemptNumber(), attempt.getExceptionCause().getCause());
                        }
                    }
                })
                .build();

        try {
            return retryer.call(() -> processGridWeatherDataToTempInternal(gridWkt, level, gridIndex, totalGrids, batchNo, tempDir, gridInfo));
        } catch (Exception e) {
            LogUtil.error("处理网格{}异常，已达到最大重试次数，放弃处理", gridIndex, e);
            return 0; // 返回0而不是抛出异常，避免影响其他网格处理
        }
    }

    /**
     * 处理单个网格的气象数据（生成到临时目录）- 内部实现
     */
    private int processGridWeatherDataToTempInternal(String gridWkt, int level, int gridIndex, int totalGrids, long batchNo, String tempDir, GridIndexInfo gridInfo) throws Exception {
        int gridFileCount = 0;

        if (level == 5) {
            throw new BizException(BizErrorCode.WEATHER_SCHEDULE_TASK_ERROR.getCode());
        }

        // 调用高德获取气象数据
        String weatherDataResponse = invokeRemoteGetWeatherData(gridWkt, level);
        if (StringUtils.isBlank(weatherDataResponse)) {
            LogUtil.info("高德返回气象数据响应为空，跳过网格{}", gridIndex);
            return gridFileCount;
        }

        // 下载并解析气象数据
        JSONObject jsonObject = JSONUtil.parseObj(weatherDataResponse);
        String dataUrl = (String) jsonObject.get("url");

        Map<String, LocationWeatherData> weatherDataMap = WeatherDataProcessUtil.downloadAndDeserializeWeatherData(dataUrl);
        if (MapUtil.isEmpty(weatherDataMap)) {
            LogUtil.warn("网格{}气象数据为空", gridIndex);
            return gridFileCount;
        }

        // 获取网格中心点坐标
        Set<String> gridCodes = weatherDataMap.keySet();
        Map<String, GridCenterCoordinate> gridCenterMap = WeatherDataProcessUtil.getGridCenterFromNode(weatherNodeUrl, gridCodes);

        // 根据factor生成相应类型的文件（生成到临时目录）
        gridFileCount = generateWeatherFilesToTemp(level, weatherDataMap, gridCenterMap, totalGrids, gridIndex, batchNo, tempDir, gridInfo);

        LogUtil.info("成功处理网格{}，生成{}个文件", gridIndex, gridFileCount);
        return gridFileCount;
    }

    /**
     * 调用高德接口获取气象数据
     */
    private String invokeRemoteGetWeatherData(String wkt, Integer level) {
        WeatherGridPostBody queryPostBody = new WeatherGridPostBody();
        queryPostBody.setBounds(wkt);
        queryPostBody.setLevel(level);

        WeatherGridRequest request = new WeatherGridRequest();
        request.setPostBody(queryPostBody);
        WeatherGridUrlParams weatherGridUrlParams = new WeatherGridUrlParams();
        request.setUrlParams(weatherGridUrlParams);

        WeatherGridQueryResponse response = (WeatherGridQueryResponse) commonDataService.fetchData(request);
        if (response == null || StringUtils.isBlank(response.getData())) {
            return null;
        }

        return response.getData();
    }

    /**
     * 根据factor生成相应的气象文件（生成到指定目录）
     */
    private int generateWeatherFilesToTemp(Integer level, Map<String, LocationWeatherData> weatherDataMap,
                                           Map<String, GridCenterCoordinate> gridCenterMap,
                                           int totalGrids, int gridIndex, long batchNo, String targetDir, GridIndexInfo gridInfo) {
        int fileCount = 0;

        try {
            long timestamp = System.currentTimeMillis();
            String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());

            // 生成文件前缀（用于标识这是定时任务生成的文件）
            String filePrefix = "schedule_weather";

            // 生成三种类型的文件：temperature, precipitation, wind
            WeatherGridFactor[] factors = {WeatherGridFactor.TEMPERATURE, WeatherGridFactor.PRECIPITATION, WeatherGridFactor.WIND_FORCE};
            String[] weatherTypeNames = {"temperature", "precipitation", "wind"};

            for (int i = 0; i < factors.length; i++) {
                WeatherGridFactor factor = factors[i];
                String weatherTypeName = weatherTypeNames[i];

                try {
                    String fileName = WeatherDataProcessUtil.generateFileName(filePrefix,
                            weatherTypeName, dateStr, totalGrids, gridIndex, timestamp);

                    String filePath = targetDir + File.separator + fileName;

                    // 根据类型生成相应的文件，写入到指定目录
                    switch (factor) {
                        case TEMPERATURE:
                            WeatherDataProcessUtil.generateTemperatureFile(fileName, level, targetDir,
                                    weatherDataMap, gridCenterMap, null);
                            break;
                        case PRECIPITATION:
                            WeatherDataProcessUtil.generatePrecipitationFile(fileName, level, targetDir,
                                    weatherDataMap, gridCenterMap, null);
                            break;
                        case WIND_FORCE:
                            // 使用按高度拆分的方法
                            List<String> windFilePaths = WeatherDataProcessUtil.generateWindFilesByHeight(
                                    filePrefix, level, targetDir, weatherDataMap, gridCenterMap, null,
                                    totalGrids, gridIndex, timestamp, dateStr);

                            // 将所有生成的文件路径添加到网格信息中
                            for (String windFilePath : windFilePaths) {
                                gridInfo.addPath(windFilePath);
                                fileCount++;
                            }

                            LogUtil.info("按高度拆分生成wind文件{}个: {}，保存到路径：{}，批次号：{}",
                                    windFilePaths.size(), windFilePaths, targetDir, batchNo);
                            break;
                        default:
                            LogUtil.warn("未知的WeatherGridFactor: {}", factor);
                            continue;
                    }

                    // 添加文件路径到网格信息中
                    gridInfo.addPath(filePath);
                    fileCount++;
                    LogUtil.info("生成{}文件: {}，保存到路径：{}，批次号：{}", weatherTypeName, fileName, targetDir, batchNo);

                } catch (Exception e) {
                    LogUtil.error("生成网格{}的{}文件失败", gridIndex, weatherTypeName, e);
                    throw e;
                }
            }

            return fileCount;

        } catch (Exception e) {
            LogUtil.error("生成网格{}气象文件失败", gridIndex, e);
            throw e;
        }
    }

    /**
     * 生成索引文件
     */
    private void generateIndexFile(List<GridIndexInfo> gridIndexList, String targetDir, long batchNo, String weatherFilePath) {
        try {
            String indexFileName = "weather_grid_index" + ".json";
            String indexFilePath = targetDir + File.separator + indexFileName;

            // 构建JSON数组
            JSONArray indexArray = new JSONArray();
            for (GridIndexInfo gridInfo : gridIndexList) {
                JSONObject gridObj = new JSONObject();
                gridObj.set("wkt", gridInfo.getWkt());

                // 将临时目录路径转换为最终的latest目录路径
                List<String> finalPaths = new ArrayList<>();
                for (String tempPath : gridInfo.getPaths()) {
                    // 将临时目录路径替换为最终的latest目录路径
                    String fileName = new File(tempPath).getName();
                    String finalPath = weatherFilePath + File.separator + fileName;
                    finalPaths.add(finalPath);
                }

                gridObj.set("paths", finalPaths);
                indexArray.add(gridObj);
            }

            // 写入文件
            try (FileWriter writer = new FileWriter(indexFilePath, StandardCharsets.UTF_8)) {
                writer.write(indexArray.toStringPretty());
                writer.flush();
            }

            LogUtil.info("成功生成索引文件：{}，包含{}个网格信息，路径指向latest目录：{}", indexFilePath, gridIndexList.size(), weatherFilePath);

        } catch (IOException e) {
            LogUtil.error("生成索引文件失败，批次号：{}", batchNo, e);
            throw new BizException(BizErrorCode.WEATHER_SCHEDULE_TASK_ERROR.getCode(),
                    "生成索引文件失败: " + e.getMessage());
        }
    }

    /**
     * 准备临时目录
     */
    private void prepareTemporaryDirectory(String tempDir) {
        try {
            File directory = new File(tempDir);

            // 如果临时目录已存在，先删除
            if (directory.exists()) {
                deleteTempDirectory(tempDir);
            }

            // 重新创建目录实例（删除后需要重新获取）
            directory = new File(tempDir);

            // 创建临时目录
            boolean created = directory.mkdirs();
            if (created) {
                LogUtil.info("创建临时天气文件目录：{}", tempDir);
            } else {
                LogUtil.error("创建临时天气文件目录失败：{}", tempDir);
                throw new BizException(BizErrorCode.FILE_DIRECTORY_CREATE_ERROR.getCode(),
                        BizErrorCode.FILE_DIRECTORY_CREATE_ERROR.getDesc());
            }

        } catch (Exception e) {
            LogUtil.error("准备临时目录失败：{}", tempDir, e);
            throw new BizException(BizErrorCode.FILE_DIRECTORY_CREATE_ERROR.getCode(),
                    BizErrorCode.FILE_DIRECTORY_CREATE_ERROR.getDesc());
        }
    }

    /**
     * 原子替换：用新数据替换旧数据
     */
    private void replaceOldDataWithNew(String tempDir, String weatherLatestFilePath) {
        try {
            File tempDirectory = new File(tempDir);
            File targetDirectory = new File(weatherLatestFilePath);

            // 1. 删除旧的目标目录（如果存在）
            if (targetDirectory.exists()) {
                cleanupDirectory(weatherLatestFilePath);
                if (!targetDirectory.delete()) {
                    LogUtil.warn("删除旧目标目录失败，但将继续执行");
                }
            }

            // 2. 将临时目录重命名为目标目录
            boolean renamed = tempDirectory.renameTo(targetDirectory);
            if (renamed) {
                LogUtil.info("成功将临时目录重命名为目标目录：{} -> {}", tempDir, weatherLatestFilePath);
            } else {
                LogUtil.error("重命名临时目录失败：{} -> {}", tempDir, weatherLatestFilePath);
                // 重命名失败时，尝试复制文件
                copyDirectoryContents(tempDir, weatherLatestFilePath);
                deleteTempDirectory(tempDir);
            }

        } catch (Exception e) {
            LogUtil.error("替换数据失败", e);
            throw new BizException(BizErrorCode.WEATHER_CLEANUP_ERROR.getCode(),
                    BizErrorCode.WEATHER_CLEANUP_ERROR.getDesc());
        }
    }

    /**
     * 复制目录内容（当重命名失败时使用）
     */
    private void copyDirectoryContents(String sourceDir, String targetDir) {
        try {
            File source = new File(sourceDir);
            File target = new File(targetDir);

            // 确保目标目录存在
            if (!target.exists()) {
                target.mkdirs();
            }

            File[] files = source.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        File targetFile = new File(target, file.getName());
                        java.nio.file.Files.copy(file.toPath(), targetFile.toPath(),
                                java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                        LogUtil.info("复制文件：{} -> {}", file.getName(), targetFile.getAbsolutePath());
                    }
                }
            }

        } catch (Exception e) {
            LogUtil.error("复制目录内容失败：{} -> {}", sourceDir, targetDir, e);
            throw new BizException(BizErrorCode.WEATHER_CLEANUP_ERROR.getCode(),
                    BizErrorCode.WEATHER_CLEANUP_ERROR.getDesc());
        }
    }

    /**
     * 清理指定目录中的所有文件
     */
    private void cleanupDirectory(String dirPath) {
        try {
            File directory = new File(dirPath);

            // 如果目录不存在，无需清理
            if (!directory.exists()) {
                LogUtil.info("目录不存在，无需清理：{}", dirPath);
                return;
            }

            // 删除目录中的所有文件
            File[] files = directory.listFiles();
            if (files != null) {
                int deletedCount = 0;
                for (File file : files) {
                    if (file.isFile()) {
                        try {
                            if (file.delete()) {
                                deletedCount++;
                                LogUtil.info("删除文件：{}", file.getName());
                            }
                        } catch (Exception e) {
                            LogUtil.error("删除文件异常：{}", file.getName(), e);
                        }
                    }
                }
                LogUtil.info("清理目录完成，共删除{}个文件，目录：{}", deletedCount, dirPath);
            } else {
                LogUtil.info("目录为空，无需清理：{}", dirPath);
            }

        } catch (Exception e) {
            LogUtil.error("清理目录失败：{}", dirPath, e);
            throw new BizException(BizErrorCode.WEATHER_CLEANUP_ERROR.getCode(),
                    BizErrorCode.WEATHER_CLEANUP_ERROR.getDesc());
        }
    }

    /**
     * 完全删除临时目录及其内容
     */
    private void deleteTempDirectory(String tempDir) {
        try {
            File directory = new File(tempDir);

            // 如果目录不存在，无需删除
            if (!directory.exists()) {
                LogUtil.info("临时目录不存在，无需删除：{}", tempDir);
                return;
            }

            // 先清理目录中的文件
            cleanupDirectory(tempDir);

            // 删除目录本身
            boolean deleted = directory.delete();
            if (deleted) {
                LogUtil.info("成功删除临时目录：{}", tempDir);
            } else {
                LogUtil.warn("删除临时目录失败：{}", tempDir);
            }

        } catch (Exception e) {
            LogUtil.error("删除临时目录失败：{}", tempDir, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 处理细分网格的气象数据
     * 1. 按照1平方公里切分WKT
     * 2. 获取高德气象数据
     * 3. 将1平方公里网格进一步切分为0.0049平方公里的小网格
     * 4. 匹配网格码与小网格，生成气象文件
     *
     * @param weatherLatestFilePath 天气文件保存路径
     */
    public void doProcessFineGridWeatherData(String weatherLatestFilePath) {
        LogUtil.info("细分网格天气数据定时任务开始执行，目标区域：{}", TARGET_WKT);

        // 生成批次号（当前时间戳）
        long batchNo = System.currentTimeMillis();

        try {
            // 1. 创建临时目录用于生成新数据
            String tempDir = weatherLatestFilePath + "_temp_" + batchNo;
            prepareTemporaryDirectory(tempDir);

            // 2. 使用WktGridUtil切分区域为1平方公里的网格
            List<String> gridWktList = WktGridUtil.splitIntoGrids(TARGET_WKT, 1.0);
            LogUtil.info("区域拆分为{}个1平方公里网格", gridWktList.size());

            if (CollectionUtils.isEmpty(gridWktList)) {
                LogUtil.warn("区域拆分网格为空，跳过处理");
                // 删除临时目录
                deleteTempDirectory(tempDir);
                return;
            }

            // 3. 用于收集网格索引信息
            List<GridIndexInfo> gridIndexList = new ArrayList<>();

            // 4. 处理每个1平方公里网格
            int totalGeneratedFiles = 0;
            int level = 5;
            final int totalGrids = gridWktList.size();

            for (int i = 0; i < gridWktList.size(); i++) {
                final String gridWkt = gridWktList.get(i);

                try {
                    GridIndexInfo gridInfo = new GridIndexInfo(gridWkt);
                    int gridFileCount = processFineGridWeatherDataToTemp(gridWkt, level, i, totalGrids, batchNo, tempDir, gridInfo);
                    totalGeneratedFiles += gridFileCount;

                    // 只有生成了文件的网格才加入索引
                    if (gridFileCount > 0) {
                        gridIndexList.add(gridInfo);
                    }
                } catch (Exception e) {
                    LogUtil.error("处理1平方公里网格{}异常", i, e);
                    // 继续处理下一个网格
                }
            }

            // 5. 验证生成的文件数量是否符合预期
            if (totalGeneratedFiles == 0) {
                LogUtil.error("未生成任何天气文件，取消替换操作");
                deleteTempDirectory(tempDir);
                throw new BizException(BizErrorCode.WEATHER_SCHEDULE_TASK_ERROR.getCode(),
                        "未生成任何天气文件");
            }

            // 6. 生成索引文件
            generateIndexFile(gridIndexList, tempDir, batchNo, weatherLatestFilePath);

            // 7. 原子替换：生成成功后替换旧数据
            replaceOldDataWithNew(tempDir, weatherLatestFilePath);

            LogUtil.info("细分网格模式：本批次共生成{}个文件，共{}个网格，已成功替换到路径：{}", totalGeneratedFiles, gridIndexList.size(), weatherLatestFilePath);
            LogUtil.info("细分网格天气数据定时任务执行完成，批次号：{}", batchNo);
        } catch (Exception e) {
            LogUtil.error("细分网格天气数据定时任务执行失败，批次号：{}", batchNo, e);
            // 删除可能存在的临时目录
            String tempDir = weatherLatestFilePath + "_temp_" + batchNo;
            deleteTempDirectory(tempDir);
            throw new BizException(BizErrorCode.WEATHER_SCHEDULE_TASK_ERROR.getCode(),
                    BizErrorCode.WEATHER_SCHEDULE_TASK_ERROR.getDesc());
        }
    }

    /**
     * 处理单个1平方公里网格的细分气象数据 - 使用Google重试框架
     */
    private int processFineGridWeatherDataToTemp(String gridWkt, int level, int gridIndex, int totalGrids, long batchNo, String tempDir, GridIndexInfo gridInfo) {
        // 创建重试器
        Retryer<Integer> retryer = RetryerBuilder.<Integer>newBuilder()
                .retryIfException() // 遇到任何异常都重试
                .withWaitStrategy(WaitStrategies.fixedWait(5, TimeUnit.SECONDS)) // 每次重试等待5秒
                .withStopStrategy(StopStrategies.stopAfterAttempt(3)) // 最多重试3次
                .withRetryListener(new RetryListener() {
                    @Override
                    public <V> void onRetry(Attempt<V> attempt) {
                        if (attempt.hasException()) {
                            LogUtil.warn("处理细分网格{}异常，第{}次重试，异常信息：{}",
                                    gridIndex, attempt.getAttemptNumber(), attempt.getExceptionCause().getMessage());
                        }
                    }
                })
                .build();

        try {
            return retryer.call(() -> processFineGridWeatherDataToTempInternal(gridWkt, level, gridIndex, totalGrids, batchNo, tempDir, gridInfo));
        } catch (Exception e) {
            LogUtil.error("处理细分网格{}异常，已达到最大重试次数，放弃处理", gridIndex, e);
            return 0; // 返回0而不是抛出异常，避免影响其他网格处理
        }
    }

    /**
     * 处理单个1平方公里网格的细分气象数据 - 内部实现
     */
    private int processFineGridWeatherDataToTempInternal(String gridWkt, int level, int gridIndex, int totalGrids, long batchNo, String tempDir, GridIndexInfo gridInfo) throws Exception {
        int gridFileCount = 0;

        // 1. 调用高德获取气象数据
        String weatherDataResponse = invokeRemoteGetWeatherData(gridWkt, level);
        if (StringUtils.isBlank(weatherDataResponse)) {
            LogUtil.info("高德返回气象数据响应为空，跳过网格{}", gridIndex);
            return gridFileCount;
        }

        // 2. 下载并解析气象数据
        JSONObject jsonObject = JSONUtil.parseObj(weatherDataResponse);
        String dataUrl = (String) jsonObject.get("url");

        Map<String, LocationWeatherData> weatherDataMap = WeatherDataProcessUtil.downloadAndDeserializeWeatherData(dataUrl);
        if (MapUtil.isEmpty(weatherDataMap)) {
            LogUtil.warn("网格{}气象数据为空", gridIndex);
            return gridFileCount;
        }

        // 3. 获取网格中心点坐标
        Set<String> gridCodes = weatherDataMap.keySet();
        Map<String, GridWkt> gridWktFromNode = WeatherDataProcessUtil.getGridWktFromNode(weatherWktUrl, gridCodes);

        // 4. 将1平方公里网格切分为0.0049平方公里的小网格
        List<String> fineGridWktList = splitIntoFineGrids(gridWkt);
        LogUtil.info("1平方公里网格{}拆分为{}个0.0049平方公里小网格", gridIndex, fineGridWktList.size());

        // 5. 为每个小网格匹配气象数据
        Map<String, LocationWeatherData> fineGridWeatherMap = matchFineGridsWithWeatherData(fineGridWktList, weatherDataMap, gridWktFromNode);

        // 6. 根据factor生成相应类型的文件（生成到临时目录）
        gridFileCount = generateFineGridWeatherFilesToTemp(level, fineGridWeatherMap, gridWktFromNode, totalGrids, gridIndex, batchNo, tempDir, gridInfo);

        LogUtil.info("成功处理细分网格{}，生成{}个文件", gridIndex, gridFileCount);
        return gridFileCount;
    }

    /**
     * 将1平方公里网格切分为0.0049平方公里的小网格
     */
    private List<String> splitIntoFineGrids(String wktString) {
        return WktGridUtil.splitIntoGrids(wktString, FINE_GRID_SIZE_KM2);
    }

    /**
     * 为每个小网格匹配气象数据
     * 匹配规则：只要找到任意一个在这0.0049平方公里内的网格，就以它的气象代表这0.0049平方公里的数据
     */
    private Map<String, LocationWeatherData> matchFineGridsWithWeatherData(List<String> fineGridWktList,
                                                                           Map<String, LocationWeatherData> weatherDataMap,
                                                                           Map<String, GridWkt> gridWktMap) {
        Map<String, LocationWeatherData> fineGridWeatherMap = new HashMap<>();
        WKTReader reader = new WKTReader();

        // 打印调试信息
        LogUtil.info("开始匹配小网格气象数据，原始气象数据数量：{}，网格WKT数量：{}，小网格数量：{}",
                weatherDataMap.size(), gridWktMap.size(), fineGridWktList.size());

        // 检查gridWktMap是否为空
        if (gridWktMap.isEmpty()) {
            LogUtil.warn("网格WKT映射为空，无法进行空间匹配，将使用默认匹配方式");

            // 如果没有WKT数据，使用简单匹配：将第一个气象数据分配给所有小网格
            if (!weatherDataMap.isEmpty()) {
                String firstKey = weatherDataMap.keySet().iterator().next();
                LocationWeatherData firstData = weatherDataMap.get(firstKey);

                for (int i = 0; i < fineGridWktList.size(); i++) {
                    fineGridWeatherMap.put("fine_grid_" + i, firstData);
                }

                LogUtil.info("使用默认匹配方式，将第一个气象数据分配给所有{}个小网格", fineGridWktList.size());
            }

            return fineGridWeatherMap;
        }

        Map<String, Geometry> gridCodeGeometryMap = new HashMap<>();
        for (String gridCode : weatherDataMap.keySet()) {
            try {
                GridWkt gridWkt = gridWktMap.get(gridCode);
                if (gridWkt != null) {
                    String gridWktStr = gridWkt.getWkt();
                    if (gridWktStr == null) {
                        LogUtil.warn("网格码{}的WKT为null", gridCode);
                        continue;
                    }
                    Geometry gridGeometry = reader.read(gridWktStr);
                    gridCodeGeometryMap.put(gridCode, gridGeometry);
                } else {
                    LogUtil.warn("网格码{}没有对应的WKT数据", gridCode);
                }
            } catch (ParseException e) {
                LogUtil.error("解析网格码{}的WKT失败", gridCode, e);
            }
        }

        LogUtil.info("成功解析{}个网格几何对象，开始匹配小网格", gridCodeGeometryMap.size());

        // 为每个小网格找到匹配的气象数据
        int matchedCount = 0;
        int unmatchedCount = 0;

        for (int i = 0; i < fineGridWktList.size(); i++) {
            String fineGridWkt = fineGridWktList.get(i);
            try {
                Geometry fineGridGeometry = reader.read(fineGridWkt);

                // 查找与小网格相交的网格码
                String matchedGridCode = null;
                for (Map.Entry<String, Geometry> entry : gridCodeGeometryMap.entrySet()) {
                    try {
                        if (fineGridGeometry.intersects(entry.getValue()) ||
                                fineGridGeometry.contains(entry.getValue()) ||
                                fineGridGeometry.covers(entry.getValue())) {
                            matchedGridCode = entry.getKey();
                            LogUtil.info("小网格{}找到匹配的网格码：{}", i, matchedGridCode);
                            break;
                        }
                    } catch (Exception e) {
                        LogUtil.error("检查小网格{}与网格码{}的空间关系时发生异常", i, entry.getKey(), e);
                    }
                }

                // 如果找到匹配的网格码，将其气象数据分配给小网格
                if (matchedGridCode != null) {
                    LocationWeatherData weatherData = weatherDataMap.get(matchedGridCode);
                    if (weatherData != null) {
                        // 使用小网格的WKT作为键
                        fineGridWeatherMap.put("fine_grid_" + i, weatherData);
                        matchedCount++;
                    } else {
                        LogUtil.warn("网格码{}没有对应的气象数据", matchedGridCode);
                        unmatchedCount++;
                    }
                } else {
                    LogUtil.info("小网格{}未找到匹配的网格码", i);
                    unmatchedCount++;
                }
            } catch (ParseException e) {
                LogUtil.error("解析小网格{}的WKT失败", i, e);
                unmatchedCount++;
            }
        }

        // 如果没有匹配到任何数据，但有气象数据可用，则使用第一个气象数据
        if (fineGridWeatherMap.isEmpty() && !weatherDataMap.isEmpty()) {
            LogUtil.warn("未匹配到任何小网格气象数据，将使用第一个气象数据");

            String firstKey = weatherDataMap.keySet().iterator().next();
            LocationWeatherData firstData = weatherDataMap.get(firstKey);

            for (int i = 0; i < fineGridWktList.size(); i++) {
                fineGridWeatherMap.put("fine_grid_" + i, firstData);
            }

            matchedCount = fineGridWktList.size();
            unmatchedCount = 0;
        }

        LogUtil.info("小网格匹配完成，成功匹配{}个，未匹配{}个，总共{}个小网格",
                matchedCount, unmatchedCount, fineGridWktList.size());
        return fineGridWeatherMap;
    }

    /**
     * 生成细分网格的气象文件
     */
    private int generateFineGridWeatherFilesToTemp(int level, Map<String, LocationWeatherData> weatherDataMap,
                                                   Map<String, GridWkt> gridWktMap,
                                                   int totalGrids, int gridIndex, long batchNo, String targetDir, GridIndexInfo gridInfo) {
        int fileCount = 0;

        try {
            long timestamp = System.currentTimeMillis();
            String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());

            // 生成文件前缀（用于标识这是细分网格定时任务生成的文件）
            String filePrefix = "fine_grid_weather";

            // 打印调试信息
            LogUtil.info("生成细分网格文件，weatherDataMap键：{}", weatherDataMap.keySet());
            LogUtil.info("生成细分网格文件，gridWktMap键：{}", gridWktMap.keySet());

            // 创建一个新的gridWktMap，使其键与weatherDataMap匹配
            Map<String, GridWkt> matchedGridWktMap = new HashMap<>();
            for (String weatherKey : weatherDataMap.keySet()) {
                // 为每个weatherKey创建一个对应的GridWkt
                GridWkt gridWkt = new GridWkt();
                gridWkt.setWkt(gridInfo.getWkt());  // 使用当前网格的WKT

                // 尝试找到一个有效的中心点
                if (!gridWktMap.isEmpty()) {
                    // 使用任意一个gridWktMap中的中心点
                    GridWkt anyGridWkt = gridWktMap.values().iterator().next();
                    if (anyGridWkt != null && anyGridWkt.getCenter() != null) {
                        gridWkt.setCenter(anyGridWkt.getCenter());
                    } else {
                        // 创建一个默认的中心点
                        GridCenterCoordinate defaultCenter = new GridCenterCoordinate();
                        defaultCenter.setLatDegree("0");
                        defaultCenter.setLngDegree("0");
                        gridWkt.setCenter(defaultCenter);
                    }
                } else {
                    // 创建一个默认的中心点
                    GridCenterCoordinate defaultCenter = new GridCenterCoordinate();
                    defaultCenter.setLatDegree("0");
                    defaultCenter.setLngDegree("0");
                    gridWkt.setCenter(defaultCenter);
                }

                // 添加到匹配的Map中
                matchedGridWktMap.put(weatherKey, gridWkt);
            }

            LogUtil.info("创建的匹配gridWktMap键：{}", matchedGridWktMap.keySet());

            // 生成三种类型的文件：temperature, precipitation, wind
            WeatherGridFactor[] factors = {WeatherGridFactor.TEMPERATURE, WeatherGridFactor.PRECIPITATION, WeatherGridFactor.WIND_FORCE};
            String[] weatherTypeNames = {"temperature", "precipitation", "wind"};

            for (int i = 0; i < factors.length; i++) {
                WeatherGridFactor factor = factors[i];
                String weatherTypeName = weatherTypeNames[i];

                try {
                    String fileName = WeatherDataProcessUtil.generateFileName(filePrefix,
                            weatherTypeName, dateStr, totalGrids, gridIndex, timestamp);

                    String filePath = targetDir + File.separator + fileName;

                    // 根据类型生成相应的文件，写入到指定目录
                    switch (factor) {
                        case TEMPERATURE:
                            WeatherDataProcessUtil.generateTemperatureFile(fileName, level, targetDir,
                                    weatherDataMap, null, matchedGridWktMap);
                            break;
                        case PRECIPITATION:
                            WeatherDataProcessUtil.generatePrecipitationFile(fileName, level, targetDir,
                                    weatherDataMap, null, matchedGridWktMap);
                            break;
                        case WIND_FORCE:
                            // 使用按高度拆分的方法
                            List<String> windFilePaths = WeatherDataProcessUtil.generateWindFilesByHeight(
                                    filePrefix, level, targetDir, weatherDataMap, null, matchedGridWktMap,
                                    totalGrids, gridIndex, timestamp, dateStr);

                            // 将所有生成的文件路径添加到网格信息中
                            for (String windFilePath : windFilePaths) {
                                gridInfo.addPath(windFilePath);
                                fileCount++;
                            }

                            LogUtil.info("按高度拆分生成wind文件{}个: {}，保存到路径：{}，批次号：{}",
                                    windFilePaths.size(), windFilePaths, targetDir, batchNo);
                            break;
                        default:
                            LogUtil.warn("未知的WeatherGridFactor: {}", factor);
                            continue;
                    }

                    // 添加文件路径到网格信息中
                    gridInfo.addPath(filePath);
                    fileCount++;
                    LogUtil.info("生成细分网格{}文件: {}，保存到路径：{}，批次号：{}", weatherTypeName, fileName, targetDir, batchNo);

                } catch (Exception e) {
                    LogUtil.error("生成细分网格{}的{}文件失败", gridIndex, weatherTypeName, e);
                    throw e;
                }
            }

            return fileCount;

        } catch (Exception e) {
            LogUtil.error("生成细分网格{}气象文件失败", gridIndex, e);
            throw e;
        }
    }
}

