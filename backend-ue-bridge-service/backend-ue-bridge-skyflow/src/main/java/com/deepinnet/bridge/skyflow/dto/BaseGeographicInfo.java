package com.deepinnet.bridge.skyflow.dto;

import lombok.Data;

/**
 * 基础地理信息模型
 * 包含位置的基本地理和气象特征
 */
@Data
public class BaseGeographicInfo {
    /**
     * 云底高（米）
     */
    private double cloudBaseHeight;

    /**
     * 能见度（米）
     */
    private double visibility;

    /**
     * 地面降雨强度
     */
    private double precipitationIntensity;

    /**
     * 地面大气气压
     */
    private double airpressure;

    /**
     * 大气湿度
     */
    private double humidity;

    /**
     * 地面温度（摄氏度）
     */
    private double temperature;
}