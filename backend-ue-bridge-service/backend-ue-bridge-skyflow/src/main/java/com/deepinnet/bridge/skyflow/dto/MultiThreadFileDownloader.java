package com.deepinnet.bridge.skyflow.dto;

import cn.hutool.http.HttpUtil;
import com.deepinnet.bridge.error.BizErrorCode;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;

import java.io.*;

public class MultiThreadFileDownloader {
    /**
     * 下载文件到本地
     *
     * @param fileUrl       远程文件地址
     * @param localFilePath 本地存储路径
     * @throws IOException 下载失败异常
     */
    public static void downloadFile(String fileUrl, String localFilePath) {
        LogUtil.info("开始下载文件，文件路径：{}", localFilePath);
        long startTime = System.currentTimeMillis();

        try (InputStream in = HttpUtil.createGet(fileUrl).execute().bodyStream();
             FileOutputStream fos = new FileOutputStream(localFilePath);
             // 64KB 缓冲区
             BufferedOutputStream bos = new BufferedOutputStream(fos, 64 * 1024)) {
            // 64KB buffer
            byte[] buffer = new byte[64 * 1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }

            long endTime = System.currentTimeMillis();
            LogUtil.info("下载文件结束，总耗时：{}，文件路径：{}", endTime - startTime, localFilePath);
        } catch (Exception e) {
            LogUtil.error("下载文件失败，文件保存路径：" + localFilePath, e);
            throw new BizException(BizErrorCode.DOWNLOAD_FILE_ERROR.getCode());
        }
    }

}
