package com.deepinnet.bridge.skyflow.dto;

import lombok.Data;
import org.locationtech.jts.geom.Point;

import java.util.List;

/**
 * 位置天气数据模型
 * 表示单个位置的天气数据，包含基础信息和所有高度层
 */
@Data
public class LocationWeatherData {
    /**
     * 位置编码
     */
    private String locationCode;

    private String center;

    private Point centerPoint;

    /**
     * 基础地理信息
     */
    private BaseGeographicInfo baseInfo;

    /**
     * 各高度层的天气数据列表
     */
    private List<WeatherLayer> weatherLayers;
}