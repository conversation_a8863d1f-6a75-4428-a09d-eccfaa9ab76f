package com.deepinnet.bridge.skyflow.task;

import com.deepinnet.bridge.skyflow.util.WeatherDataScheduleUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 天气数据定时任务
 * 每5分钟获取指定区域的气象信息，生成天气文件并持久化记录
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class WeatherDataScheduleTask {

    @Value("${weather.file}")
    private String weatherLatestFilePath;

    @Value("${weather.sliceFile}")
    private String weatherSliceGridFilePath;

    @Value("${weather.simpleFile}")
    private String weatherSimpleFilePath;

    @Resource
    private WeatherDataScheduleUtil weatherDataScheduleUtil;

    /**
     * 定时任务：每5分钟执行一次，处理所有网格的气象数据
     */
    @Scheduled(fixedRate = 5, timeUnit = TimeUnit.MINUTES)
    public void processWeatherData() {
        LogUtil.info("开始执行普通天气数据定时任务");
        weatherDataScheduleUtil.doProcessWeatherData(false, null, weatherLatestFilePath);
    }


    /**
     * 定时任务：每5分钟执行一次
     */
    //@Scheduled(fixedRate = 5, timeUnit = TimeUnit.MINUTES)
    public void processSimpleWeatherData() {
        weatherDataScheduleUtil.doProcessWeatherData(true, "simple", weatherSimpleFilePath);
    }

    /**
     * 定时任务：每5分钟执行一次，处理细分网格的气象数据
     * 1. 按照1平方公里切分WKT
     * 2. 获取高德气象数据
     * 3. 将1平方公里网格进一步切分为0.0049平方公里的小网格
     * 4. 匹配网格码与小网格，生成气象文件
     */
    //@Scheduled(fixedRate = 5, timeUnit = TimeUnit.MINUTES)
    public void processFineGridWeatherData() {
        weatherDataScheduleUtil.doProcessFineGridWeatherData(weatherSliceGridFilePath);
    }
} 