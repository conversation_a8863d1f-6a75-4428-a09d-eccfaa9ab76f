package com.deepinnet.bridge.skyflow.util;

import com.deepinnet.digitaltwin.common.log.LogUtil;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.io.*;
import org.locationtech.jts.operation.buffer.*;
import org.locationtech.jts.precision.GeometryPrecisionReducer;

import java.util.*;

/**
 * WKT网格划分工具类
 * 用于将大区域WKT切分为指定面积大小的小网格
 */
public class WktGridUtil {
    /**
     * 默认网格大小为1平方公里
     */
    private static final double DEFAULT_GRID_SIZE_KM2 = 1.0;
    
    /**
     * 高精度几何工厂，用于处理复杂几何计算
     */
    private static final PrecisionModel HIGH_PRECISION_MODEL = new PrecisionModel(1000000);
    private static final GeometryFactory HIGH_PRECISION_FACTORY = new GeometryFactory(HIGH_PRECISION_MODEL);
    
    /**
     * 将WKT多边形切分为约20平方公里的小网格
     * 
     * @param wktString 输入的WKT字符串（可能包含SRID信息，格式如：WKT | SRID）
     * @return 切分后的WKT字符串列表
     */
    public static List<String> splitInto20Km2Grids(String wktString) {
        return splitIntoGrids(wktString, DEFAULT_GRID_SIZE_KM2);
    }
    
    /**
     * 将WKT多边形切分为指定平方公里的小网格
     * 
     * @param wktString 输入的WKT字符串（可能包含SRID信息，格式如：WKT | SRID）
     * @param gridSizeKm2 网格大小，单位为平方公里
     * @return 切分后的WKT字符串列表
     */
    public static List<String> splitIntoGrids(String wktString, double gridSizeKm2) {
        try {
            // 提取SRID
            String srid = "4326"; // 默认SRID
            if (wktString.contains("|")) {
                String[] parts = wktString.split("\\|");
                wktString = parts[0].trim();
                if (parts.length > 1) {
                    srid = parts[1].trim();
                }
            }
            
            WKTReader reader = new WKTReader(HIGH_PRECISION_FACTORY);
            Geometry geometry = reader.read(wktString);
            
            // 预处理几何图形以避免拓扑异常
            geometry = preprocessGeometry(geometry);
            
            // 获取原始多边形的边界框
            Envelope envelope = geometry.getEnvelopeInternal();
            double minX = envelope.getMinX();
            double minY = envelope.getMinY();
            double maxX = envelope.getMaxX();
            double maxY = envelope.getMaxY();
            
            // 计算中心点纬度（用于计算经度步长）
            double centerY = (minY + maxY) / 2.0;
            
            // 计算网格边长（公里）
            double gridSideKm = Math.sqrt(gridSizeKm2);
            
            // 计算纬度和经度的步长（1度纬度约111公里）
            double latStep = gridSideKm / 111.0;
            // 经度步长需要根据纬度调整（经度间距随纬度增加而减小）
            double lngStep = latStep / Math.cos(Math.toRadians(centerY));
            
            LogUtil.info("计算步长 - latStep: {}, lngStep: {}", latStep, lngStep);
            
            // 确保边界框有足够的大小进行切分
            if (Math.abs(maxX - minX) < lngStep * 0.5) {
                LogUtil.warn("边界框X方向过小({}), 扩展边界", maxX - minX);
                maxX = minX + lngStep;
            }
            
            if (Math.abs(maxY - minY) < latStep * 0.5) {
                LogUtil.warn("边界框Y方向过小({}), 扩展边界", maxY - minY);
                maxY = minY + latStep;
            }
            
            List<String> gridWkts = new ArrayList<>();
            WKTWriter writer = new WKTWriter();

            LogUtil.info("开始切分WKT，边界框：({}, {}) - ({}, {}), 网格大小: {}平方公里, 步长: 纬度{}度, 经度{}度",
                     minX, minY, maxX, maxY, gridSizeKm2, latStep, lngStep);
            
            int totalGrids = 0;
            int successfulGrids = 0;
            int failedGrids = 0;
            
            // 使用基于索引的循环，确保能够精确控制循环次数
            int gridRows = Math.max(1, (int)Math.ceil((maxY - minY) / latStep));
            int gridCols = Math.max(1, (int)Math.ceil((maxX - minX) / lngStep));
            
            LogUtil.info("网格划分: {}行 x {}列", gridRows, gridCols);
            
            for (int i = 0; i < gridCols; i++) {
                double x = minX + i * lngStep;
                for (int j = 0; j < gridRows; j++) {
                    double y = minY + j * latStep;
                    totalGrids++;
                    try {
                        // 创建网格单元
                        Polygon gridCell = createGridCell(x, y, lngStep, latStep);
                        
                        // 只保留与原始区域相交的网格
                        if (gridCell.intersects(geometry)) {
                            // 安全计算相交部分
                            Geometry intersection = safeIntersection(gridCell, geometry);
                            if (intersection != null && !intersection.isEmpty() && intersection.getArea() > 0) {
                                // 转换回WKT格式并添加SRID
                                String gridWkt = writer.write(intersection) + " | " + srid;
                                gridWkts.add(gridWkt);
                                successfulGrids++;
                            }
                        }
                    } catch (Exception e) {
                        failedGrids++;
                        LogUtil.warn("单个网格处理失败，位置：({}, {})，错误：{}", x, y, e.getMessage());
                        // 继续处理下一个网格，不抛出异常
                    }
                }
            }
            
            LogUtil.info("WKT切分完成，总网格数：{}，成功：{}，失败：{}，最终生成：{}个有效网格", 
                    totalGrids, successfulGrids, failedGrids, gridWkts.size());
            return gridWkts;
        } catch (ParseException e) {
            LogUtil.error("WKT解析错误: {}", e.getMessage(), e);
            throw new RuntimeException("WKT解析错误: " + e.getMessage(), e);
        }
    }
    
    /**
     * 预处理几何图形以避免拓扑异常
     * 
     * @param geometry 原始几何图形
     * @return 预处理后的几何图形
     */
    private static Geometry preprocessGeometry(Geometry geometry) {
        try {
            if (geometry == null || geometry.isEmpty()) {
                LogUtil.warn("输入几何图形为空，无法进行预处理");
                return geometry;
            }
            
            LogUtil.info("预处理前几何图形 - 类型: {}, 有效性: {}, 坐标数: {}", 
                geometry.getGeometryType(), geometry.isValid(), geometry.getCoordinates().length);
            
            Geometry result = geometry;
            
            // 1. 检查并修复几何图形的有效性
            if (!result.isValid()) {
                LogUtil.warn("输入几何图形无效，尝试修复");
                // 使用buffer(0)来修复无效几何图形
                Geometry buffered = result.buffer(0);
                if (buffered != null && !buffered.isEmpty()) {
                    result = buffered;
                    LogUtil.info("使用buffer(0)修复后 - 有效性: {}", result.isValid());
                } else {
                    LogUtil.warn("buffer(0)修复失败，保留原始几何图形");
                }
            }
            
            // 2. 使用高精度模型标准化几何图形
            try {
                GeometryPrecisionReducer reducer = new GeometryPrecisionReducer(HIGH_PRECISION_MODEL);
                Geometry reduced = reducer.reduce(result);
                if (reduced != null && !reduced.isEmpty()) {
                    result = reduced;
                    LogUtil.info("精度减少后 - 有效性: {}", result.isValid());
                } else {
                    LogUtil.warn("精度减少失败，保留当前几何图形");
                }
            } catch (Exception e) {
                LogUtil.warn("精度减少异常: {}", e.getMessage());
            }
            
            // 3. 应用微小的缓冲区来消除边界问题
            // 使用1e-8度（约1毫米）的微小缓冲区
            try {
                double microBufferSize = 1e-8;
                BufferParameters bufferParams = new BufferParameters();
                bufferParams.setQuadrantSegments(4);
                BufferOp bufferOp = new BufferOp(result, bufferParams);
                Geometry buffered = bufferOp.getResultGeometry(microBufferSize);
                if (buffered != null && !buffered.isEmpty()) {
                    result = buffered;
                    LogUtil.info("微缓冲区处理后 - 有效性: {}", result.isValid());
                } else {
                    LogUtil.warn("微缓冲区处理失败，保留当前几何图形");
                }
            } catch (Exception e) {
                LogUtil.warn("微缓冲区处理异常: {}", e.getMessage());
            }
            
            if (result == null || result.isEmpty()) {
                LogUtil.warn("预处理导致几何图形为空，回退到原始几何图形");
                return geometry;
            }
            
            LogUtil.info("几何图形预处理完成，有效性：{}", result.isValid());
            return result;
            
        } catch (Exception e) {
            LogUtil.warn("几何图形预处理失败，使用原始几何图形：{}", e.getMessage());
            return geometry;
        }
    }
    
    /**
     * 创建网格单元
     * 
     * @param x 起始X坐标
     * @param y 起始Y坐标
     * @param lngStep 经度步长
     * @param latStep 纬度步长
     * @return 网格多边形
     */
    private static Polygon createGridCell(double x, double y, double lngStep, double latStep) {
        Coordinate[] coords = new Coordinate[5];
        coords[0] = new Coordinate(x, y);
        coords[1] = new Coordinate(x + lngStep, y);
        coords[2] = new Coordinate(x + lngStep, y + latStep);
        coords[3] = new Coordinate(x, y + latStep);
        coords[4] = new Coordinate(x, y); // 闭合多边形
        
        LinearRing ring = HIGH_PRECISION_FACTORY.createLinearRing(coords);
        return HIGH_PRECISION_FACTORY.createPolygon(ring);
    }
    
    /**
     * 安全计算两个几何图形的交集，包含多种容错机制
     * 
     * @param geom1 几何图形1
     * @param geom2 几何图形2
     * @return 交集结果，如果失败返回null
     */
    private static Geometry safeIntersection(Geometry geom1, Geometry geom2) {
        // 策略1：直接计算交集
        try {
            return geom1.intersection(geom2);
        } catch (TopologyException e) {
            LogUtil.info("直接交集计算失败，尝试缓冲区方法：{}", e.getMessage());
        } catch (Exception e) {
            LogUtil.warn("直接交集计算异常：{}", e.getMessage());
        }
        
        // 策略2：使用微小缓冲区重试
        try {
            double bufferSize = 1e-10;
            Geometry bufferedGeom1 = geom1.buffer(bufferSize);
            Geometry bufferedGeom2 = geom2.buffer(bufferSize);
            return bufferedGeom1.intersection(bufferedGeom2);
        } catch (Exception e) {
            LogUtil.info("缓冲区交集计算失败，尝试精度降低方法：{}", e.getMessage());
        }
        
        // 策略3：降低精度重试
        try {
            PrecisionModel lowPrecisionModel = new PrecisionModel(1000);
            GeometryFactory lowPrecisionFactory = new GeometryFactory(lowPrecisionModel);
            GeometryPrecisionReducer reducer = new GeometryPrecisionReducer(lowPrecisionModel);
            
            Geometry reducedGeom1 = reducer.reduce(geom1);
            Geometry reducedGeom2 = reducer.reduce(geom2);
            
            // 重新创建几何图形使用低精度工厂
            reducedGeom1 = lowPrecisionFactory.createGeometry(reducedGeom1);
            reducedGeom2 = lowPrecisionFactory.createGeometry(reducedGeom2);
            
            return reducedGeom1.intersection(reducedGeom2);
        } catch (Exception e) {
            LogUtil.info("精度降低交集计算失败，尝试简单重叠检查：{}", e.getMessage());
        }
        
        // 策略4：如果所有策略都失败，检查简单的包含关系
        try {
            if (geom1.contains(geom2)) {
                return geom2;
            } else if (geom2.contains(geom1)) {
                return geom1;
            } else if (geom1.intersects(geom2)) {
                // 返回网格区域作为近似结果
                LogUtil.warn("无法计算精确交集，返回网格区域作为近似结果");
                return geom1;
            }
        } catch (Exception e) {
            LogUtil.warn("包含关系检查也失败：{}", e.getMessage());
        }
        
        LogUtil.warn("所有交集计算策略都失败，跳过当前网格");
        return null;
    }

    /**
     * 估算WKT多边形的面积（平方公里）
     * 注意：此方法为近似计算，在大区域或高纬度地区误差较大
     * 
     * @param wktString WKT字符串
     * @return 估算面积（平方公里）
     */
    public static double estimateAreaInKm2(String wktString) {
        try {
            // 去除SRID部分
            if (wktString.contains("|")) {
                wktString = wktString.split("\\|")[0].trim();
            }
            
            WKTReader reader = new WKTReader();
            Geometry geometry = reader.read(wktString);
            
            // 获取边界框
            Envelope envelope = geometry.getEnvelopeInternal();
            double minY = envelope.getMinY(); // 最小纬度
            double maxY = envelope.getMaxY(); // 最大纬度
            double centerY = (minY + maxY) / 2.0; // 中心纬度
            
            // 计算面积（平方度）
            double areaInSquareDegrees = geometry.getArea();
            
            // 转换为平方公里（近似值）
            // 1度纬度约111公里，1度经度在赤道约111公里，但随纬度增加而减小
            double degToKmFactor = 111.0 * 111.0 * Math.cos(Math.toRadians(centerY));
            double areaInKm2 = areaInSquareDegrees * degToKmFactor;
            
            return areaInKm2;
        } catch (ParseException e) {
            LogUtil.error("WKT解析错误: {}", e.getMessage(), e);
            throw new RuntimeException("WKT解析错误: " + e.getMessage(), e);
        }
    }
    
    /**
     * 计算切分后会产生的网格数量
     * 
     * @param wktString WKT字符串
     * @param gridSizeKm2 网格大小（平方公里）
     * @return 预计网格数量
     */
    public static int estimateGridCount(String wktString, double gridSizeKm2) {
        double areaKm2 = estimateAreaInKm2(wktString);
        return (int) Math.ceil(areaKm2 / gridSizeKm2);
    }
} 