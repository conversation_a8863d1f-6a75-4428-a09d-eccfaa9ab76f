package com.deepinnet.bridge.error;

import com.deepinnet.digitaltwin.common.error.*;
import lombok.*;

/**
 * <AUTHOR> wong
 * @create 2024/7/29 15:15
 * @Description
 */
@AllArgsConstructor
@Getter
public enum BizErrorCode {


    /*------------------------------------------------------------------------*/
    /*                        通用事件[0000开头]                                  */
    /*------------------------------------------------------------------------*/

    /**
     * 未知异常
     */
    UNKNOWN_EXCEPTION(ErrorLevel.ERROR, ErrorType.SYSTEM, "0000000", "未知异常"),

    /**
     * 请求参数非法
     */
    ILLEGAL_PARAMS(ErrorLevel.ERROR, ErrorType.BIZ, "0000001", "参数错误"),


    /**
     * 并发操作
     */
    OPERATION_CONCURRENT_EXCP(ErrorLevel.ERROR, ErrorType.BIZ, "0000003", "并发处理异常"),

    /**
     * 系统暂时不支持
     */
    SYSTEM_NOT_SUPPORT(ErrorLevel.ERROR, ErrorType.SYSTEM, "0000004", "系统暂时不支持"),

    NETWORK_REQUEST_ERROR(ErrorLevel.ERROR, ErrorType.SYSTEM, "0000005", "网络请求异常"),

    /**
     * 读取文件失败
     */
    READ_FILE_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "0000006", "读取数据错误"),

    /**
     * 数据异常
     */
    DATA_INVALID(ErrorLevel.ERROR, ErrorType.BIZ, "0000007", "数据异常"),

    /**
     * 业务幂等
     **/
    IDEMPOTENT_REQUEST_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "0000995", "业务幂等"),

    /**
     * 非法请求
     **/
    ILLEGAL_REQUEST(ErrorLevel.ERROR, ErrorType.BIZ, "0000996", "非法请求"),

    /**
     * 服务熔断不可用
     */
    SERVICE_FUSING_NOT_AVAILABLE(ErrorLevel.WARN, ErrorType.SYSTEM, "0000997", "服务熔断不可用"),

    /**
     * 系统限流
     */
    SLA_LIMIT_ERROR(ErrorLevel.WARN, ErrorType.SYSTEM, "0000998", "SLA限流异常"),

    /**
     * 系统内部错误
     */
    INTERNAL_SERVER_ERROR(ErrorLevel.ERROR, ErrorType.SYSTEM, "0000999", "系统开小差了"),

    FILE_SIZE_LIMIT_EXCEEDED_ERROR(ErrorLevel.ERROR, ErrorType.SYSTEM, "00001001", "当前上传的文件大小超过1MB，无法处理"),


    /*------------------------------------------------------------------------*/
    /*                       气象[2000开头]                             */
    /*------------------------------------------------------------------------*/
    DESIALIZATION_FAILED(ErrorLevel.ERROR, ErrorType.BIZ, "20005", "反序列化失败"),

    INVOKE_WEATHER_CLIENT_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "20006", "调用气象网格数据失败"),

    MESSAGE_SAVE_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "20007", "消息保存失败"),

    REAL_TIME_FLIGHT_SAVE_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "20008", "实时飞行数据保存失败"),

    FLIGHT_PLAN_NOT_FOUND(ErrorLevel.ERROR, ErrorType.BIZ, "20009", "飞行计划不存在"),

    WEATHER_GRID_CALCULATE_MAX_WIND_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "20010", "天气网格计算最大风速失败，数据缺失"),

    DOWNLOAD_FILE_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "20011", "下载文件失败"),

    YUAN_FEI_PLAN_QUERY_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "20012", "鸢飞计划查询失败"),

    UAV_LIVE_STREAM_QUERY_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "20013", "无人机直播流查询失败"),

    FLIGHT_RECORD_SAVE_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "20014", "飞行记录数据保存失败"),

    WEATHER_SCHEDULE_TASK_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "20015", "天气数据定时任务执行失败"),

    FILE_DIRECTORY_CREATE_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "20016", "文件目录创建失败"),

    GET_WEATHER_GRID_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "20017", "查询气象数据失败"),

    WEATHER_CLEANUP_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "20018", "天气文件清理失败"),
    ;

    /**
     * 错误级别
     */
    private ErrorLevel errorLevel;

    /**
     * 错误类型
     */
    private ErrorType errorType;

    /**
     * 错误编码
     */
    private String code;

    /**
     * 错误描述
     */
    private String desc;
}
