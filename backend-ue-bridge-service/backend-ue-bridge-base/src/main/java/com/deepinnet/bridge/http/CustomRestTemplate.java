package com.deepinnet.bridge.http;

import org.apache.http.conn.ssl.*;
import org.apache.http.impl.client.*;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.context.annotation.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.security.cert.X509Certificate;

/**
 * <AUTHOR>
 * @version 2024-09-18
 */
@Configuration
public class CustomRestTemplate {

    @Bean
    public RestTemplate restTemplate() throws Exception {
        // 创建忽略证书的 SSLContext
        SSLContext sslContext = SSLContextBuilder.create()
                .loadTrustMaterial((TrustStrategy) (X509Certificate[] chain, String authType) -> true) // 信任所有证书
                .build();

        // 创建 SSLConnectionSocketFactory
        SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(sslContext, (hostname, session) -> true); // 信任所有主机名

        // 创建 HttpClient 并使用自定义的 SSLConnectionSocketFactory
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(socketFactory)
                .build();

        // 设置 HttpClient 到 RestTemplate
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
        // 设置连接超时为5秒
        factory.setConnectTimeout(10_000);
        // 设置读取超时为5秒
        factory.setReadTimeout(10_000);

        return new RestTemplate(factory);
    }

}
