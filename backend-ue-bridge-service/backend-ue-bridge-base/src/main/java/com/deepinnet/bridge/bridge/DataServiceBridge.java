package com.deepinnet.bridge.bridge;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.deepinnet.spatiotemporalplatform.common.request.BridgeParams;
import com.deepinnet.spatiotemporalplatform.common.response.BridgeDataResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2024-11-13
 */
@Component
@Slf4j
public class DataServiceBridge {

    @Value("${data.service.url:localhost}")
    private String dataServiceUrl;

    private static final String DATA_SERVICE_PATH = "/stpf/bridge/self";

    @Resource
    private RestTemplate restTemplate;

    /**
     * @param fetchParams
     * @return
     */
    public BridgeDataResponse callDataService(String fetchParams) {
        try {
            BridgeParams bridgeParams = BridgeParams.builder().dataFetchParam(fetchParams).build();
            // 获取数据
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<BridgeParams> entity = new HttpEntity<>(bridgeParams, headers);
            ResponseEntity<BridgeDataResponse> exchange = restTemplate.exchange(dataServiceUrl + DATA_SERVICE_PATH, HttpMethod.POST, entity, BridgeDataResponse.class);
            if (exchange.getBody() == null) {
                return new BridgeDataResponse();
            }

            // 还原数据类型
            BridgeDataResponse bridgeDataResponse = exchange.getBody();
            if (bridgeDataResponse.getDataClassName() == null) {
                // 数据类型为空，说明没有数据返回
                return bridgeDataResponse;
            }

            Class<?> clazz = Class.forName(bridgeDataResponse.getDataClassName());
            if (bridgeDataResponse.isList()) {
                // 如果是列表
                bridgeDataResponse.setData(JSON.parseArray(JSON.toJSONString(bridgeDataResponse.getData()), clazz));
            } else {
                bridgeDataResponse.setData(BeanUtil.copyProperties(bridgeDataResponse.getData(), clazz));
            }

            return bridgeDataResponse;
        } catch (Exception e) {
            log.error("调用数据服务失败", e);
            return null;
        }
    }

}
