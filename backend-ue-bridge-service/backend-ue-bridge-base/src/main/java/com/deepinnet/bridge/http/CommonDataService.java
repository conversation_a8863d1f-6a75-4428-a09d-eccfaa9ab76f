package com.deepinnet.bridge.http;

import com.alibaba.fastjson2.*;
import com.deepinnet.bridge.bridge.DataServiceBridge;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.spatiotemporalplatform.common.request.HttpApiRequest;
import com.deepinnet.spatiotemporalplatform.common.response.BridgeDataResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 判断当前部署环境，决策是调用本地时空数据服务，还是走政府网络边界通道：例如衢州政府网闸
 *
 * <AUTHOR>
 * @version 2024-07-30
 */
@Service
@Slf4j
public class CommonDataService {

    @Resource
    private DataServiceBridge dataServiceBridge;

    public Object fetchData(HttpApiRequest request) {
        // 直接调用时空数据服务的API
        return callDataService(request);
    }

    private Object callDataService(HttpApiRequest request) {
        long start = System.currentTimeMillis();
        String fetchParams = JSON.toJSONString(request, JSONWriter.Feature.WriteClassName);
        BridgeDataResponse bridgeDataResponse = dataServiceBridge.callDataService(fetchParams);
        LogUtil.info("调用时空数据服务成功,耗时:{}ms", System.currentTimeMillis() - start);
        if (bridgeDataResponse == null) {
            LogUtil.error("调用时空数据服务失败");
            return null;
        }
        return bridgeDataResponse.getData();
    }
}
