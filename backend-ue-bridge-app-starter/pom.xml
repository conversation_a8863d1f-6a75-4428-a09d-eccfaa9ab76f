<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.deepinnet</groupId>
        <artifactId>backend-ue-bridge</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <groupId>com.deepinnet</groupId>
    <artifactId>backend-ue-bridge-app-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>backend-ue-bridge-app-starter</name>
    <description>UE桥接服务应用启动器</description>

    <dependencies>
        <dependency>
            <groupId>com.deepinnet</groupId>
            <artifactId>backend-ue-bridge-web</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>backend-ue-bridge</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.6</version>
                <configuration>
                    <mainClass>com.deepinnet.bridge.BackendUeBridgeApplication</mainClass>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <!--可以把依赖的包都打包到生成的Jar包中-->
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 测试需要的插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M5</version>
                <configuration>
                    <!--suppress UnresolvedMavenProperty -->
                    <argLine>${argLine} -Djacoco.agent.destfile=target/jacoco.exec</argLine>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <executions>
                    <execution>
                        <id>report</id>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jacoco-initialize</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <phase>verify</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                    <include>**/*.json</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <!-- 二进制资源，不允许 filtering -->
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.docx</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>

</project> 