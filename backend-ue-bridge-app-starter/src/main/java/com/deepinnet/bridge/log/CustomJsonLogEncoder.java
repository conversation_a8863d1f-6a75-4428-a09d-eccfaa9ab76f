package com.deepinnet.bridge.log;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.encoder.EncoderBase;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.*;

import java.nio.charset.StandardCharsets;

/**

 *
 * <AUTHOR>
 * @since 2025-01-02 星期四
 * 自定义json日志编码器
 **/
public class CustomJsonLogEncoder extends EncoderBase<ILoggingEvent> {

    private final ObjectMapper objectMapper;

    public CustomJsonLogEncoder() {
        // 在构造函数中配置 ObjectMapper 保留空值的 key
        objectMapper = new ObjectMapper();
        objectMapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, true); // 使用 WRITE_NULLS 保留空值字段
    }


    @Override
    public byte[] headerBytes() {
        return new byte[0];
    }

    @Override
    public byte[] encode(ILoggingEvent event) {
        try {
            // 构造日志对象
            LogEvent logEvent = new LogEvent();
            logEvent.setTimestamp(event.getTimeStamp());
            logEvent.setLoggerName(event.getLoggerName());
            logEvent.setThreadName(event.getThreadName());
            logEvent.setLevel(event.getLevel().toString());

            // 处理嵌套的 JSON 格式，避免被当作字符串输出
            String message = event.getFormattedMessage();
            JsonNode messageJsonNode = parseJson(message);
            logEvent.setMessage(messageJsonNode);

            // 转换为 JSON 字符串
            String json = objectMapper.writeValueAsString(logEvent);
            return (json + System.lineSeparator()).getBytes(StandardCharsets.UTF_8);
        } catch (JsonProcessingException e) {
            // 如果序列化失败，返回原始消息
            return (event.getFormattedMessage() + System.lineSeparator()).getBytes(StandardCharsets.UTF_8);
        }
    }

    @Override
    public byte[] footerBytes() {
        return new byte[0];
    }

    /**
     * 递归解析嵌套的 JSON 字符串
     */
    private JsonNode parseJson(String jsonString) {
        try {
            // 解析 JSON 字符串为 JsonNode 对象
            JsonNode node = objectMapper.readTree(jsonString);

            // 如果是嵌套的 JSON 字符串，递归解析
            if (node.isTextual()) {
                // 如果是字符串，尝试解析为 JSON
                return objectMapper.readTree(node.asText());
            }

            return node;
        } catch (JsonProcessingException e) {
            // 如果无法解析 JSON 字符串，则返回原始值
            return objectMapper.createObjectNode().put("error", "Invalid JSON");
        }
    }

}
