package com.deepinnet.bridge;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * UE后端桥接服务应用启动类
 * 
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = "com.deepinnet")
//@MapperScan("com.deepinnet.bridge.dal.mapper")
@EnableScheduling
public class BackendUeBridgeApplication {

    public static void main(String[] args) {
        SpringApplication.run(BackendUeBridgeApplication.class, args);
    }
} 