<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.deepinnet</groupId>
    <artifactId>backend-ue-bridge</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>backend-ue-bridge</name>
    <description>UE后端桥接服务</description>

    <properties>
        <java.version>11</java.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <maven.compiler.release>11</maven.compiler.release>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!-- Spring Boot 相关版本 -->
        <spring-boot.version>2.6.13</spring-boot.version>
        <spring-cloud-starter-openfeign.version>3.1.9</spring-cloud-starter-openfeign.version>

        <!-- 数据库相关版本 -->
        <mybatis-plus.version>3.5.7</mybatis-plus.version>
        <pageHelper.version>2.1.0</pageHelper.version>
        <jsqlparser.version>4.7</jsqlparser.version>

        <!-- 工具类库版本 -->
        <hutool-all.version>5.8.18</hutool-all.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <commons-net.version>3.11.0</commons-net.version>
        <guava.version>32.1.1-jre</guava.version>
        <guava-retrying.version>2.0.0</guava-retrying.version>

        <!-- JSON 相关版本 -->
        <fastjson2.version>2.0.25</fastjson2.version>

        <!-- 地理空间相关版本 -->
        <jts-core.version>1.19.0</jts-core.version>
        <proj4j.version>1.0.0</proj4j.version>

        <!-- 代码生成相关版本 -->
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.0.Final</mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>

        <!-- 网络通信相关版本 -->
        <httpclient.version>4.5.13</httpclient.version>
        <cxf-spring-boot-starter-jaxws.version>3.4.5</cxf-spring-boot-starter-jaxws.version>
        <webservices-rt.version>2.4.4</webservices-rt.version>

        <!-- 校验相关版本 -->
        <validation-api.version>2.0.1.Final</validation-api.version>

        <!-- 安全相关版本 -->
        <jasypt.version>3.0.5</jasypt.version>

        <!-- 阿里云相关版本 -->
        <aliyun-oss.version>3.15.1</aliyun-oss.version>

        <!-- 深因科技相关版本 -->
        <digital-boot-starter.version>1.0.0.2025.05.26-RELEASE</digital-boot-starter.version>
        <spatiotemporalplatform-api-model.version>20250627-SNAPSHOT</spatiotemporalplatform-api-model.version>
        <digitaltwincommon.version>1.3-SNAPSHOT</digitaltwincommon.version>

        <!-- 插件版本 -->
        <maven-compiler-plugin.version>3.8.0</maven-compiler-plugin.version>
        <maven-source-plugin.version>3.0.1</maven-source-plugin.version>
    </properties>

    <modules>
        <module>backend-ue-bridge-api-model</module>
        <module>backend-ue-bridge-dal</module>
        <module>backend-ue-bridge-service</module>
        <module>backend-ue-bridge-web</module>
        <module>backend-ue-bridge-app-starter</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Cloud 相关 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${spring-cloud-starter-openfeign.version}</version>
            </dependency>

            <!-- 数据库相关 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pageHelper.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.github.jsqlparser</groupId>
                        <artifactId>jsqlparser</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>${jsqlparser.version}</version>
            </dependency>

            <!-- 工具类库 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commons-net.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.rholder</groupId>
                <artifactId>guava-retrying</artifactId>
                <version>${guava-retrying.version}</version>
            </dependency>

            <!-- JSON 相关 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <!-- 地理空间相关 -->
            <dependency>
                <groupId>org.locationtech.jts</groupId>
                <artifactId>jts-core</artifactId>
                <version>${jts-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.locationtech.proj4j</groupId>
                <artifactId>proj4j</artifactId>
                <version>${proj4j.version}</version>
            </dependency>

            <!-- 代码生成相关 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${lombok-mapstruct-binding.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- 网络通信相关 -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
                <version>${cxf-spring-boot-starter-jaxws.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.metro</groupId>
                <artifactId>webservices-rt</artifactId>
                <version>${webservices-rt.version}</version>
            </dependency>

            <!-- 校验相关 -->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>

            <!-- 安全相关 -->
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>${jasypt.version}</version>
            </dependency>

            <!-- 阿里云相关 -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-oss.version}</version>
            </dependency>

            <!-- 深因科技相关 -->
            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>digital-boot-starter</artifactId>
                <version>${digital-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>digitaltwincommon</artifactId>
                <version>${digitaltwincommon.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>spatiotemporalplatform-api-model</artifactId>
                <version>${spatiotemporalplatform-api-model.version}</version>
            </dependency>

            <!-- 项目内部依赖 -->
            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>backend-ue-bridge-api-model</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>backend-ue-bridge-dal</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>backend-ue-bridge-service</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>backend-ue-bridge-base</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>backend-ue-bridge-skyflow</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>backend-ue-bridge-web</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>backend-ue-bridge-app-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.deepinnet</groupId>
            <artifactId>digitaltwincommon</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.locationtech.proj4j</groupId>
            <artifactId>proj4j</artifactId>
        </dependency>

        <dependency>
            <groupId>org.locationtech.jts</groupId>
            <artifactId>jts-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <release>${java.version}</release>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <compilerArgs>
                            <arg>-parameters</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven-source-plugin.version}</version>
                    <configuration>
                        <attach>true</attach>
                    </configuration>
                    <executions>
                        <execution>
                            <phase>compile</phase>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <excludes>
                            <exclude>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </exclude>
                        </excludes>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project> 